<?php

namespace App\Jobs\QAAutomation;

use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\QAAutomation\QAAutomationLog;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\QAAutomation\QAAutomationService;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class QAAutomationLastInitialSaleAttempt implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public function __construct()
    {
        $this->queue = QueueHelperService::QUEUE_NAME_QA_AUTOMATION;
    }

    public function handle(
        QAAutomationService $service,
        ConsumerProductRepository $consumerProductRepository,
        ProductProcessingService $productProcessingService,
    ): void
    {
        //get all the leads that are 5 min old and 2 hours and still in initial
        $consumerProducts = ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, function (Builder $builder) {
                $builder->whereNot(Consumer::FIELD_CLASSIFICATION, Consumer::CLASSIFICATION_EMAIL_ONLY);
            })
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_INITIAL)
            ->where(ConsumerProduct::CREATED_AT, '<', Carbon::now()->subMinutes(5))
            ->where(ConsumerProduct::CREATED_AT, '>', Carbon::now()->subHour())
            ->get();

        /** @var ConsumerProduct $consumerProduct */
        foreach($consumerProducts as $consumerProduct) {
            //attempt to qualify
            if(!$service->qualifyConsumerProduct($consumerProduct->id)) {
                $service->checkNotReservedAndReserveProduct($consumerProduct);

                //get the latest log and use the entry for the failed reason
                $qaLog = QAAutomationLog::query()
                    ->where(QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
                    ->latest()
                    ->first();

                $reason = $qaLog ? "QA Automation: " . $qaLog->entry : 'Failed QA Automation';

                $consumerProductRepository->updateConsumerProductStatus($consumerProduct, ConsumerProduct::STATUS_PENDING_REVIEW);

                try {
                    $productProcessingService->moveToPendingReview(
                        consumerProduct: $consumerProduct,
                        processor: LeadProcessor::systemProcessor(),
                        reason: $reason,
                    );
                } catch(Throwable $e) {
                    logger()->error("QA auto failed to move ". $consumerProduct->id . " to pending review", [$e->getMessage()]);
                }
            } else {
                try {
                    $service->processLead($consumerProduct->id);
                } catch(Throwable $e) {
                    logger()->error("QA auto failed to process ". $consumerProduct->id, [$e->getMessage()]);
                }
            }
        }
    }
}